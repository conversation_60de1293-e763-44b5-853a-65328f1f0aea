{"name": "sahai-cep-v2", "version": "2.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build && node copy-cep-files.js", "preview": "vite preview", "lint": "eslint . --ext ts,tsx,js,jsx --report-unused-disable-directives", "lint:fix": "eslint . --ext ts,tsx,js,jsx --fix"}, "dependencies": {"lucide-react": "^0.408.0", "react": "^18.3.1", "react-dom": "^18.3.1", "shiki": "^1.10.3", "zustand": "^4.5.2"}, "devDependencies": {"@eslint/js": "^9.31.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.19", "eslint": "^8.57.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "typescript": "^5.4.5", "vite": "^5.3.3", "vite-plugin-singlefile": "^2.3.0"}}