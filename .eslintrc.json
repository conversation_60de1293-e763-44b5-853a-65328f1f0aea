{"root": true, "env": {"browser": true, "es2020": true, "node": true}, "extends": ["eslint:recommended"], "ignorePatterns": ["dist", "build", "node_modules", "client/CSInterface.js", "*.config.js", "*.config.ts", "vite.config.ts", "tailwind.config.js", "postcss.config.js", "**/*.d.ts", "build-test.js", "copy-cep-files.js"], "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "settings": {"react": {"version": "detect"}}, "rules": {"no-console": "warn", "no-debugger": "error", "no-alert": "error", "no-eval": "error", "no-implied-eval": "error", "no-new-func": "error", "no-script-url": "error", "prefer-const": "error", "no-var": "error", "no-unused-vars": "off", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn"}, "overrides": [{"files": ["host/**/*.jsx", "host/**/*.jsxinc"], "env": {"browser": false, "node": false, "es6": false}, "globals": {"app": "readonly", "$": "readonly", "File": "readonly", "Folder": "readonly", "Socket": "readonly"}, "parserOptions": {"ecmaVersion": 5, "sourceType": "script"}, "rules": {"no-var": "off", "prefer-const": "off", "no-undef": "off", "no-redeclare": "off", "@typescript-eslint/no-unused-vars": "off", "no-unused-vars": "off"}}, {"files": ["**/*.ts", "**/*.tsx"], "extends": ["plugin:@typescript-eslint/recommended"], "rules": {"no-undef": "off", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}]}}]}