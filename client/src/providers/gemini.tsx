import React from 'react';
import { BaseProviderComponent, PROVIDER_CONFIGS } from '../components/ui/BaseProviderComponent';

interface Props {
  onClose: () => void;
}

export const GeminiProvider: React.FC<Props> = ({ onClose }) => {
  const handleSave = (_config: { apiKey?: string; baseURL?: string; selectedModelId: string }) => {
    // Handle the save logic here - you might need to update your store
    onClose();
  };

  return (
    <BaseProviderComponent
      config={PROVIDER_CONFIGS.gemini}
      onSave={handleSave}
    />
  );
};
