/**
 * CEP Integration Utilities
 * Provides a wrapper around the CSInterface API for Adobe CEP extensions
 */

import { CSInterface, CSEvent, ExtendScriptResponse, AppInfo, DocumentInfo, SystemInfo, Model } from '../types/cep';

// Global CSInterface instance
let csInterface: CSInterface | null = null;

// Check if we're running in a CEP environment
export const isCEPEnvironment = (): boolean => {
  return typeof window !== 'undefined' && !!(window as any).CSInterface;
};

// Get or create CSInterface instance
export const getCSInterface = (): CSInterface | null => {
  if (!csInterface && isCEPEnvironment()) {
    try {
      csInterface = new (window as any).CSInterface();
      console.log('CSInterface initialized successfully');
    } catch (error) {
      console.error('Failed to initialize CSInterface:', error);
    }
  }
  return csInterface;
};

// Initialize CEP environment
export const initializeCEP = (): void => {
  if (!isCEPEnvironment()) {
    console.warn('Not running in CEP environment');
    return;
  }

  const cs = getCSInterface();
  if (!cs) return;

  // Set up theme change listener
  cs.addEventListener('com.adobe.csxs.events.ThemeColorChanged', (event: CSEvent) => {
    console.log('Theme changed:', event);
    // Handle theme changes here
  });

  // Log host environment
  const hostEnv = cs.getHostEnvironment();
  console.log('Host environment:', hostEnv);

  // Test ExtendScript communication
  cs.evalScript('SahAI.getAppInfo()', (result: string) => {
    try {
      if (!result || result.trim() === '') {
        console.warn('Empty response from ExtendScript');
        return;
      }
      const parsed = JSON.parse(result);
      console.log('ExtendScript response:', parsed);
    } catch (error) {
      console.error('Failed to parse ExtendScript response:', error, 'Raw result:', result);
    }
  });
};

// Execute ExtendScript code with enhanced error handling and timeout
import { DEFAULT_TIMEOUT, DEFAULT_RETRIES, BASE_RETRY_DELAY } from './constants';

// Helper functions for ExtendScript execution
const isRetryableError = (result: string): boolean => {
  return result.startsWith('EvalScript error') || !result || result.trim() === '';
};

const parseExtendScriptResponse = (result: string): ExtendScriptResponse => {
  try {
    return JSON.parse(result);
  } catch {
    // If it's not JSON, treat as plain string
    return { success: true, data: result };
  }
};

const shouldRetryResponse = (parsed: ExtendScriptResponse): boolean => {
  return typeof parsed === 'object' && parsed !== null && parsed.success === false;
};

export const executeExtendScript = (code: string, timeout: number = DEFAULT_TIMEOUT, retries: number = DEFAULT_RETRIES): Promise<ExtendScriptResponse> => {
  return new Promise((resolve, reject) => {
    const cs = getCSInterface();
    if (!cs) {
      reject(new Error('CSInterface not available - not running in CEP environment'));
      return;
    }

    let attemptCount = 0;

    const attemptExecution = () => {
      attemptCount++;

      // Set up timeout
      const timeoutId = setTimeout(() => {
        if (attemptCount <= retries) {
          console.warn(`ExtendScript execution attempt ${attemptCount} timed out, retrying...`);
          attemptExecution();
        } else {
          reject(new Error(`ExtendScript execution timed out after ${timeout}ms (${retries + 1} attempts)`));
        }
      }, timeout);

      try {
        cs.evalScript(code, (result: string) => {
          clearTimeout(timeoutId);

          try {
            // Check for retryable errors
            if (isRetryableError(result)) {
              if (attemptCount <= retries) {
                console.warn(`Retryable error on attempt ${attemptCount}, retrying...`);
                setTimeout(attemptExecution, BASE_RETRY_DELAY);
                return;
              }
              reject(new Error(result.startsWith('EvalScript error')
                ? `ExtendScript Error: ${result}`
                : 'Empty response from ExtendScript after all retries'));
              return;
            }

            // Parse response
            const parsed = parseExtendScriptResponse(result);

            // Check if response indicates failure and should be retried
            if (shouldRetryResponse(parsed)) {
              if (attemptCount <= retries) {
                console.warn(`ExtendScript returned failure on attempt ${attemptCount}, retrying...`);
                setTimeout(attemptExecution, BASE_RETRY_DELAY);
                return;
              }
              reject(new Error(parsed.message || 'ExtendScript execution failed'));
              return;
            }

            // Success case
            if (typeof parsed === 'object' && parsed !== null) {
              resolve(parsed);
            } else {
              resolve({ success: true, data: parsed });
            }
          } catch (error) {
            if (attemptCount <= retries) {
              console.warn(`Error processing response on attempt ${attemptCount}, retrying...`);
              setTimeout(attemptExecution, BASE_RETRY_DELAY);
              return;
            }
            reject(new Error(`Failed to process ExtendScript response: ${error}`));
          }
        });
      } catch (error) {
        clearTimeout(timeoutId);
        if (attemptCount <= retries) {
          console.warn(`Error executing ExtendScript on attempt ${attemptCount}, retrying...`);
          setTimeout(attemptExecution, BASE_RETRY_DELAY);
          return;
        }
        reject(new Error(`Failed to execute ExtendScript: ${error}`));
      }
    };

    attemptExecution();
  });
};

// Get application information
export const getAppInfo = async (): Promise<ExtendScriptResponse & { data?: AppInfo }> => {
  return executeExtendScript('SahAI.getAppInfo()');
};

// Get document information
export const getDocumentInfo = async (): Promise<ExtendScriptResponse & { data?: DocumentInfo }> => {
  return executeExtendScript('SahAI.getDocumentInfo()');
};

// Execute code in host application
export const executeCode = async (code: string, language: string): Promise<ExtendScriptResponse> => {
  return executeExtendScript(`SahAI.executeCode(${JSON.stringify(code)}, ${JSON.stringify(language)})`);
};

// Show alert in host application
export const showAlert = async (message: string, title?: string): Promise<ExtendScriptResponse> => {
  return executeExtendScript(`SahAI.showAlert(${JSON.stringify(message)}, ${JSON.stringify(title || 'SahAI')})`);
};

// Log message to ExtendScript console
export const logToExtendScript = async (message: string, level?: string): Promise<ExtendScriptResponse> => {
  return executeExtendScript(`SahAI.log(${JSON.stringify(message)}, ${JSON.stringify(level || 'info')})`);
};

// Get system information
export const getSystemInfo = async (): Promise<ExtendScriptResponse & { data?: SystemInfo }> => {
  return executeExtendScript('SahAI.getSystemInfo()');
};

// Close the extension panel
export const closeExtension = (): void => {
  const cs = getCSInterface();
  if (cs) {
    cs.closeExtension();
  }
};

// Get extension ID
export const getExtensionId = (): string | null => {
  const cs = getCSInterface();
  return cs ? cs.getExtensionID() : null;
};

// Get system path
export const getSystemPath = (pathType: string): string | null => {
  const cs = getCSInterface();
  return cs ? cs.getSystemPath(pathType) : null;
};

// Open URL in default browser
export const openURLInBrowser = (url: string): void => {
  const cs = getCSInterface();
  if (cs) {
    cs.openURLInDefaultBrowser(url);
  }
};

interface CEPSettingsData {
  activeProviderId?: string;
  providers?: Array<{
    id: string;
    isConfigured: boolean;
    apiKey?: string;
    baseURL?: string;
    selectedModelId?: string;
    settings?: Record<string, unknown>;
  }>;
}

import { SETTINGS_STORAGE_KEY, DEFAULT_MODEL_TIMEOUT } from './constants';

export class CEPSettings {
  private static readonly SETTINGS_KEY = SETTINGS_STORAGE_KEY;

  static async save(data: CEPSettingsData): Promise<void> {
    const settingsString = JSON.stringify(data);

    try {
      // Try to save to CEP persistent storage first
      if (isCEPEnvironment()) {
        try {
          const result = await executeExtendScript(`saveSettings(${JSON.stringify(data)})`, DEFAULT_MODEL_TIMEOUT);
          if (result.success) {
            console.log('Settings saved to CEP storage successfully');
          } else {
            throw new Error(result.message || 'CEP save failed');
          }
        } catch (cepError) {
          console.warn('CEP storage save failed, falling back to localStorage:', cepError);
          // Continue to localStorage fallback
        }
      }

      // Always save to localStorage as backup
      localStorage.setItem(this.SETTINGS_KEY, settingsString);
      console.log('Settings saved to localStorage successfully');

    } catch (error) {
      console.error('All settings save methods failed:', error);
      // Last resort: try localStorage one more time
      try {
        localStorage.setItem(this.SETTINGS_KEY, settingsString);
      } catch (localError) {
        throw new Error(`Failed to save settings: ${error}. LocalStorage also failed: ${localError}`);
      }
    }
  }

  static async load(): Promise<CEPSettingsData> {
    try {
      // Define priority order for loading settings
      const loadStrategies = [
        {
          name: 'CEP Storage',
          load: async () => {
            if (isCEPEnvironment()) {
              const result = await executeExtendScript('loadSettings()', DEFAULT_MODEL_TIMEOUT);
              if (result.success) {
                // Check if we got actual data or just empty defaults
                if (result.data && Object.keys(result.data).length > 0) {
                  return result.data;
                }
                // If CEP storage is empty, return null to try next strategy
                return null;
              }
            }
            return null;
          }
        },
        {
          name: 'LocalStorage',
          load: async () => {
            const localData = localStorage.getItem(this.SETTINGS_KEY);
            if (localData) {
              try {
                const parsed = JSON.parse(localData);
                // Check if localStorage has actual data or just empty defaults
                if (parsed && Object.keys(parsed).length > 0) {
                  return parsed;
                }
              } catch (parseError) {
                console.warn('Failed to parse localStorage settings:', parseError);
              }
            }
            return null;
          }
        }
      ];

      // Try each strategy in order
      for (const strategy of loadStrategies) {
        try {
          const data = await strategy.load();
          if (data) {
            console.log(`Settings loaded from ${strategy.name} successfully`);
            return data;
          }
        } catch (strategyError) {
          console.warn(`Failed to load settings from ${strategy.name}:`, strategyError);
        }
      }

      console.log('No existing settings found in any storage, returning defaults');
      return { providers: [] };

    } catch (error) {
      console.error('All settings load methods failed:', error);
      // Return empty settings as last resort
      return { providers: [] };
    }
  }

  static async exportSettings(): Promise<string> {
    const settings = await this.load();
    return JSON.stringify(settings, null, 2);
  }

  static async importSettings(settingsJson: string): Promise<void> {
    try {
      const settings = JSON.parse(settingsJson);
      await this.save(settings);
    } catch {
      throw new Error('Invalid settings format');
    }
  }

  static async clearSettings(): Promise<void> {
    try {
      // Clear CEP storage
      if (isCEPEnvironment()) {
        try {
          await executeExtendScript('saveSettings({})', DEFAULT_MODEL_TIMEOUT);
        } catch (cepError) {
          console.warn('Failed to clear CEP storage:', cepError);
        }
      }

      // Clear localStorage
      localStorage.removeItem(this.SETTINGS_KEY);
      console.log('Settings cleared successfully');

    } catch (error) {
      throw new Error(`Failed to clear settings: ${error}`);
    }
  }
}

// Provider status checker
export class ProviderStatusChecker {
  static async checkProviderStatus(providerId: string, config: { apiKey?: string; baseURL?: string }): Promise<{
    isOnline: boolean;
    latency?: number;
    error?: string;
  }> {
    const startTime = Date.now();
    
    try {
      // Use CEP bridge to check status
      const { ProviderBridge } = await import('./cepIntegration');
      const models = await ProviderBridge.listModels(
        providerId,
        config.baseURL,
        config.apiKey
      );
      
      return {
        isOnline: (models as any[]).length > 0,
        latency: Date.now() - startTime
      };
    } catch (error: any) {
      return {
        isOnline: false,
        error: (error as Error).message || String(error),
        latency: Date.now() - startTime
      };
    }
  }
}

// Add Promise timeout polyfill for CEP-safe operations
declare global {
  interface Promise<T> {
    timeout(ms: number): Promise<T>;
  }
}

if (!Promise.prototype.timeout) {
  Promise.prototype.timeout = function<T>(this: Promise<T>, ms: number): Promise<T> {
    return Promise.race([
      this,
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error(`Operation timed out after ${ms}ms`)), ms)
      )
    ]);
  };
}

// Provider Bridge for model listing with enhanced error handling and retry
export const ProviderBridge = {
  async listModels(providerId: string, baseUrl?: string, apiKey?: string): Promise<Model[]> {
    return new Promise((resolve, reject) => {
      const cs = getCSInterface();
      if (!cs) {
        reject(new Error('CSInterface not available - not running in CEP environment'));
        return;
      }

      const script = `listModels("${providerId}", "${baseUrl || ''}", "${apiKey || ''}")`;

      cs.evalScript(script, (result: string) => {
        try {
          // Check for an empty or invalid result from ExtendScript
          if (!result || result === 'undefined') {
            return reject(new Error('ExtendScript returned an empty response.'));
          }

          const parsedResult = JSON.parse(result);

          // Check if ExtendScript returned a structured error
          if (parsedResult.error) {
            return reject(new Error(`Host Script Error: ${parsedResult.message}`));
          }

          // Success, resolve with the array of models
          // Transform the models to match the expected interface
          const transformedModels: Model[] = (parsedResult as any[]).map((m: any) => ({
            id: m.id || 'unknown-id',
            name: m.name || m.id || 'Unknown Model',
            description: m.description || '',
            contextLength: m.contextLength || m.context_length || 4096,
            isRecommended: m.isRecommended || m.is_recommended || false
          }));

          resolve(transformedModels);

        } catch (e: any) {
          // Catch JSON parsing errors or other client-side issues
          reject(new Error(`Failed to parse response from host: ${e.message}. Response: ${result}`));
        }
      });
    });
  },

  async pullModel(providerId: string, modelName: string, baseUrl: string): Promise<{ success: boolean; message: string }> {
    if (providerId !== 'ollama') throw new Error('Pull only supported for Ollama');
    return new Promise((resolve, reject) => {
      const cs = getCSInterface();
      if (!cs) {
        reject(new Error('CSInterface not available - not running in CEP environment'));
        return;
      }

      // Use the makeRequest function to pull the model
      const ollamaHost = baseUrl.replace('http://', '').split(':')[0];
      const ollamaPort = parseInt(baseUrl.split(':')[2] || '11434', 10);
      const script = `
        try {
          var result = makeRequest('${ollamaHost}', '/api/pull', 'POST', null, ${ollamaPort}, JSON.stringify({name: '${modelName}'}));
          JSON.stringify({success: true, message: 'Model pull initiated'});
        } catch (e) {
          JSON.stringify({success: false, message: e.toString()});
        }
      `;

      cs.evalScript(script, (result: string) => {
        try {
          const parsed = JSON.parse(result);
          if (parsed.success) {
            resolve(parsed);
          } else {
            reject(new Error(parsed.message));
          }
        } catch (parseError) {
          reject(new Error(`Failed to parse pull response: ${parseError}`));
        }
      });
    });
  },

  async getFallbackModels(providerId: string): Promise<Model[]> {
    // Import dynamically to avoid circular dependencies
    const { getFallbackModels } = await import('./modelData');
    return getFallbackModels(providerId);
  }
};

// Helper for default URLs (expand as needed) - currently unused
// function getDefaultUrlForProvider(id: string): string {
//   const urls: Record<string, string> = {
//     openai: 'https://api.openai.com/v1',
//     anthropic: 'https://api.anthropic.com/v1',
//     gemini: 'https://generativelanguage.googleapis.com/v1beta',
//     groq: 'https://api.groq.com/openai/v1',
//     deepseek: 'https://api.deepseek.com/v1',
//     mistral: 'https://api.mistral.ai/v1',
//     moonshot: 'https://api.moonshot.cn/v1',
//     openrouter: 'https://openrouter.ai/api/v1',
//     perplexity: 'https://api.perplexity.ai',
//     qwen: 'https://dashscope.aliyuncs.com/api/v1',
//     together: 'https://api.together.xyz/v1',
//     vertex: 'https://generativelanguage.googleapis.com/v1beta',
//     xai: 'https://api.x.ai/v1',
//     ollama: 'http://localhost:11434',
//     lmstudio: 'http://localhost:1234'
//   };
//   return urls[id] || '';
// }
