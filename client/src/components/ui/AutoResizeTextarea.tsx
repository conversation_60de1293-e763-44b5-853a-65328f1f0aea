import React, { useRef, useEffect, useCallback, forwardRef } from 'react';

interface AutoResizeTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  minHeight?: number;
  maxHeight?: number;
  onHeightChange?: (height: number) => void;
}

/**
 * Auto-resizing textarea component that grows/shrinks based on content
 * Replaces direct DOM manipulation with a clean React component
 */
export const AutoResizeTextarea = forwardRef<HTMLTextAreaElement, AutoResizeTextareaProps>(
  ({ 
    value, 
    onChange, 
    minHeight = 72, 
    maxHeight = 200, 
    onHeightChange,
    className = '',
    style,
    ...props 
  }, ref) => {
    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const heightRef = useRef<number>(minHeight);

    // Combine refs
    const combinedRef = useCallback((node: HTMLTextAreaElement) => {
      textareaRef.current = node;
      if (typeof ref === 'function') {
        ref(node);
      } else if (ref) {
        ref.current = node;
      }
    }, [ref]);

    const calculateHeight = useCallback(() => {
      const textarea = textareaRef.current;
      if (!textarea) return minHeight;

      // Temporarily set height to auto to get scroll height
      textarea.style.height = 'auto';
      const scrollHeight = textarea.scrollHeight;
      
      // Calculate new height within bounds
      const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);
      
      // Apply the height
      textarea.style.height = `${newHeight}px`;
      
      return newHeight;
    }, [minHeight, maxHeight]);

    const handleChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
      onChange(e);
      
      // Calculate new height after state update
      requestAnimationFrame(() => {
        const newHeight = calculateHeight();
        if (newHeight !== heightRef.current) {
          heightRef.current = newHeight;
          onHeightChange?.(newHeight);
        }
      });
    }, [onChange, calculateHeight, onHeightChange]);

    // Reset height when value is cleared
    useEffect(() => {
      if (!value && textareaRef.current) {
        textareaRef.current.style.height = `${minHeight}px`;
        if (heightRef.current !== minHeight) {
          heightRef.current = minHeight;
          onHeightChange?.(minHeight);
        }
      }
    }, [value, minHeight, onHeightChange]);

    // Initial height calculation
    useEffect(() => {
      if (textareaRef.current) {
        const initialHeight = calculateHeight();
        heightRef.current = initialHeight;
        onHeightChange?.(initialHeight);
      }
    }, [calculateHeight, onHeightChange]);

    return (
      <textarea
        ref={combinedRef}
        value={value}
        onChange={handleChange}
        className={`resize-none transition-all duration-150 ease-out ${className}`}
        style={{
          minHeight: `${minHeight}px`,
          maxHeight: `${maxHeight}px`,
          height: `${minHeight}px`,
          ...style,
        }}
        {...props}
      />
    );
  }
);

AutoResizeTextarea.displayName = 'AutoResizeTextarea';
