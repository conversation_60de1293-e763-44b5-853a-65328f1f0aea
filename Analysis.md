## Root Cause Analysis

The error occurs because ExtendScript is returning an error message (starting with "<PERSON>rro<PERSON>" - hence the "E" at position 0) instead of valid JSON. This typically happens when:
1. ExtendScript functions are not found
2. There are syntax errors in the ExtendScript files
3. File paths are incorrect
4. `#include` statements are broken

## Solution

### 1. Fix ExtendScript File Structure

First, create a proper extension directory structure. The error suggests the extension might not be installed correctly:

```
~/Adobe/CEP/extensions/SahAI/
├── CSXS/
│   └── manifest.xml
├── client/
│   ├── index.html
│   └── CSInterface.js
├── host/
│   ├── ae-integration.jsxinc
│   ├── constants.jsx
│   └── modelData.jsx
└── icons/
    ├── icon-16.png
    └── icon-32.png
```

### 2. Fix Constants Definition in ExtendScript

In `host/constants.jsx`, change from `var` to proper global variables:

```javascript
// File paths - Change these lines
SETTINGS_FILE_PATH = "~/Adobe/CEP/extensions/SahAI/settings.json";
HISTORY_FILE_PATH = "~/Adobe/CEP/extensions/SahAI/history.json";
EXTENSION_DIR_PATH = "~/Adobe/CEP/extensions/SahAI";
```

### 3. Add Directory Creation in Initialization

Update the `SahAI.init()` function in `host/ae-integration.jsxinc` to ensure directories exist:

```javascript
SahAI.init = function() {
    try {
        // Set up error handling
        $.level = 1; // Enable debugging
        
        // Ensure extension directory exists
        var settingsDir = new Folder(EXTENSION_DIR_PATH);
        if (!settingsDir.exists) {
            if (!settingsDir.create()) {
                throw new Error("Failed to create extension directory: " + EXTENSION_DIR_PATH);
            }
        }
        
        // Log initialization
        $.writeln("SahAI ExtendScript initialized successfully");
        
        return {
            success: true,
            message: "ExtendScript initialized",
            version: "2.0.0"
        };
    } catch (error) {
        $.writeln("Error initializing SahAI ExtendScript: " + error.toString());
        return {
            success: false,
            message: error.toString()
        };
    }
};
```

### 4. Fix File Path Handling

Update the file functions in `host/ae-integration.jsxinc` to handle potential path issues:

```javascript
// Update loadSettings function
function loadSettings() {
    try {
        // Ensure directory exists
        var settingsDir = new Folder(EXTENSION_DIR_PATH);
        if (!settingsDir.exists) {
            settingsDir.create();
        }
        
        var settingsFile = new File(SETTINGS_FILE_PATH);
        if (settingsFile.exists) {
            if (!settingsFile.open("r")) {
                throw new Error("Failed to open settings file for reading");
            }
            var content = settingsFile.read();
            settingsFile.close();
            
            if (content.trim() === '') {
                return JSON.stringify({ success: true, data: {} });
            }
            
            var parsedContent = JSON.parse(content);
            return JSON.stringify({ success: true, data: parsedContent });
        }
        return JSON.stringify({ success: true, data: {} });
    } catch (error) {
        $.writeln("Error loading settings: " + error.toString());
        return JSON.stringify({ success: false, message: error.toString(), data: {} });
    }
}

// Update saveSettings function
function saveSettings(settings) {
    try {
        var settingsDir = new Folder(EXTENSION_DIR_PATH);
        if (!settingsDir.exists) {
            if (!settingsDir.create()) {
                throw new Error("Failed to create extension directory");
            }
        }

        var settingsFile = new File(SETTINGS_FILE_PATH);
        if (!settingsFile.open("w")) {
            throw new Error("Failed to open settings file for writing");
        }
        settingsFile.write(JSON.stringify(settings));
        settingsFile.close();

        return JSON.stringify({ success: true });
    } catch (error) {
        $.writeln("Error saving settings: " + error.toString());
        return JSON.stringify({ success: false, message: error.toString() });
    }
}
```

### 5. Add Better Error Handling to Provider Functions

Update the `listModels` function in `host/ae-integration.jsxinc` to provide more detailed error messages:

```javascript
function listModels(providerId, baseURL, apiKey) {
    try {
        // Validate inputs
        if (!providerId) {
            throw new Error("Provider ID is required");
        }
        
        var responseBody;
        var models = [];

        // ... existing implementation ...

        return JSON.stringify(models);

    } catch (e) {
        var errorMessage = "Error in listModels(" + providerId + "): " + e.toString();
        $.writeln(errorMessage);
        
        // Return proper error format that client can parse
        return JSON.stringify({
            success: false,
            error: true,
            message: e.toString(),
            providerId: providerId
        });
    }
}
```

### 6. Update Manifest Script Path

Ensure the manifest.xml points to the correct script path:

```xml
<Resources>
    <MainPath>./client/index.html</MainPath>
    <ScriptPath>./host/ae-integration.jsxinc</ScriptPath>
    <!-- ... rest of CEFCommandLine ... -->
</Resources>
```

### 7. Fix Client-Side Error Handling

Update the `ProviderBridge.listModels` function in `src/utils/cepIntegration.ts` to better handle ExtendScript errors:

```javascript
export const ProviderBridge = {
  async listModels(providerId: string, baseUrl?: string, apiKey?: string): Promise<Model[]> {
    return new Promise((resolve, reject) => {
      const cs = getCSInterface();
      if (!cs) {
        reject(new Error('CSInterface not available - not running in CEP environment'));
        return;
      }

      const script = `listModels("${providerId}", "${baseUrl || ''}", "${apiKey || ''}")`;

      cs.evalScript(script, (result: string) => {
        try {
          // Check for an empty or invalid result from ExtendScript
          if (!result || result.trim() === '' || result.trim() === ' undefined') {
            return reject(new Error('ExtendScript returned an empty response. Check if the script is properly loaded.'));
          }

          // Check if result starts with "EvalScript error"
          if (result.startsWith('EvalScript error')) {
            return reject(new Error(`ExtendScript execution failed: ${result}`));
          }

          const parsedResult = JSON.parse(result);

          // Check if ExtendScript returned a structured error
          if (parsedResult.error) {
            return reject(new Error(`Host Script Error: ${parsedResult.message || 'Unknown error'}`));
          }

          // Success, resolve with the array of models
          const transformedModels: Model[] = (parsedResult as any[]).map((m: any) => ({
            id: m.id || 'unknown-id',
            name: m.name || m.id || 'Unknown Model',
            description: m.description || '',
            contextLength: m.contextLength || m.context_length || 4096,
            isRecommended: m.isRecommended || m.is_recommended || false
          }));

          resolve(transformedModels);

        } catch (e: any) {
          // Catch JSON parsing errors or other client-side issues
          reject(new Error(`Failed to parse response from host: ${e.message}. Response: ${result}`));
        }
      });
    });
  }
};
```

### 8. Enable Debug Mode

Create a `.debug` file in your extension directory with the following content to enable debugging:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<ExtensionList>
  <Extension Id="com.sahai.cep.panel">
    <HostList>
      <Host Name="AEFT" Port="8088"/>
      <Host Name="PPRO" Port="8089"/>
      <Host Name="PHXS" Port="8090"/>
      <Host Name="ILST" Port="8091"/>
      <Host Name="AUDT" Port="8092"/>
    </HostList>
  </Extension>
</ExtensionList>
```

## Testing Steps

1. Ensure the extension is properly installed in `~/Adobe/CEP/extensions/SahAI/`
2. Restart Adobe application
3. Check ExtendScript console for any initialization errors
4. Try loading settings and models again

These changes should resolve the "EvalScript error" and JSON parsing issues by ensuring:
1. Proper file paths and directory structure
2. Better error handling in both ExtendScript and client code
3. Proper initialization of the extension directory
4. More informative error messages for debugging