/**
 * Shared model data for ExtendScript host
 * This file serves as the single source of truth for model information
 * Used by host scripts - JavaScript version of client/src/utils/modelData.ts
 */

// Constants are loaded globally by the host environment
// This file assumes constants.jsx has been loaded first

// Fallback model data
var FALLBACK_MODELS = {
  openai: [
    { 
      id: 'gpt-4o', 
      name: 'GPT-4o', 
      description: 'Most capable OpenAI model', 
      context_length: LARGE_CONTEXT_LENGTH, 
      is_recommended: true 
    },
    { 
      id: 'gpt-4o-mini', 
      name: 'GPT-4o Mini', 
      description: 'Faster, more affordable', 
      context_length: LARGE_CONTEXT_LENGTH, 
      is_recommended: false 
    },
    { 
      id: 'gpt-4-turbo', 
      name: 'GPT-4 Turbo', 
      description: 'Previous generation flagship', 
      context_length: LARGE_CONTEXT_LENGTH, 
      is_recommended: false 
    },
    { 
      id: 'gpt-3.5-turbo', 
      name: 'GPT-3.5 Turbo', 
      description: 'Legacy model', 
      context_length: 16384, 
      is_recommended: false 
    }
  ],
  
  anthropic: [
    { 
      id: 'claude-3-5-sonnet-20241022', 
      name: 'Claude 3.5 Sonnet', 
      description: 'Anthropic\'s most capable model', 
      context_length: EXTRA_LARGE_CONTEXT_LENGTH, 
      is_recommended: true 
    },
    { 
      id: 'claude-3-5-haiku-20241022', 
      name: 'Claude 3.5 Haiku', 
      description: 'Fast and efficient', 
      context_length: EXTRA_LARGE_CONTEXT_LENGTH, 
      is_recommended: false 
    },
    { 
      id: 'claude-3-opus-20240229', 
      name: 'Claude 3 Opus', 
      description: 'Powerful reasoning', 
      context_length: EXTRA_LARGE_CONTEXT_LENGTH, 
      is_recommended: false 
    },
    { 
      id: 'claude-2.1', 
      name: 'Claude 2.1', 
      description: 'Previous generation', 
      context_length: EXTRA_LARGE_CONTEXT_LENGTH, 
      is_recommended: false 
    }
  ],
  
  gemini: [
    { 
      id: 'gemini-1.5-pro', 
      name: 'Gemini 1.5 Pro', 
      description: 'Google\'s most capable model', 
      context_length: 2000000, 
      is_recommended: true 
    },
    { 
      id: 'gemini-1.5-flash', 
      name: 'Gemini 1.5 Flash', 
      description: 'Fast and efficient', 
      context_length: 1000000, 
      is_recommended: false 
    },
    { 
      id: 'gemini-1.0-pro', 
      name: 'Gemini 1.0 Pro', 
      description: 'Previous generation', 
      context_length: MEDIUM_CONTEXT_LENGTH, 
      is_recommended: false 
    }
  ],
  
  groq: [
    { 
      id: 'llama-3.1-70b-versatile', 
      name: 'Llama 3.1 70B', 
      description: 'Balanced performance', 
      context_length: 131072, 
      is_recommended: true 
    },
    { 
      id: 'llama-3.1-8b-instant', 
      name: 'Llama 3.1 8B', 
      description: 'Fast inference', 
      context_length: 131072, 
      is_recommended: false 
    },
    { 
      id: 'mixtral-8x7b-32768', 
      name: 'Mixtral 8x7B', 
      description: 'Large context', 
      context_length: MEDIUM_CONTEXT_LENGTH, 
      is_recommended: false 
    }
  ],
  
  deepseek: [
    { 
      id: 'deepseek-chat', 
      name: 'DeepSeek Chat', 
      description: 'General purpose', 
      context_length: LARGE_CONTEXT_LENGTH, 
      is_recommended: true 
    },
    { 
      id: 'deepseek-coder', 
      name: 'DeepSeek Coder', 
      description: 'Code-focused', 
      context_length: LARGE_CONTEXT_LENGTH, 
      is_recommended: false 
    }
  ],
  
  mistral: [
    { 
      id: 'mistral-large-latest', 
      name: 'Mistral Large', 
      description: 'Most capable', 
      context_length: LARGE_CONTEXT_LENGTH, 
      is_recommended: true 
    },
    { 
      id: 'mistral-medium-latest', 
      name: 'Mistral Medium', 
      description: 'Balanced', 
      context_length: MEDIUM_CONTEXT_LENGTH, 
      is_recommended: false 
    },
    { 
      id: 'mistral-small-latest', 
      name: 'Mistral Small', 
      description: 'Fast and efficient', 
      context_length: MEDIUM_CONTEXT_LENGTH, 
      is_recommended: false 
    }
  ],
  
  moonshot: [
    { 
      id: 'moonshot-v1-128k', 
      name: 'Moonshot v1 128K', 
      description: 'Large context', 
      context_length: LARGE_CONTEXT_LENGTH, 
      is_recommended: true 
    },
    { 
      id: 'moonshot-v1-32k', 
      name: 'Moonshot v1 32K', 
      description: 'Medium context', 
      context_length: MEDIUM_CONTEXT_LENGTH, 
      is_recommended: false 
    },
    { 
      id: 'moonshot-v1-8k', 
      name: 'Moonshot v1 8K', 
      description: 'Small context', 
      context_length: SMALL_CONTEXT_LENGTH, 
      is_recommended: false 
    }
  ],
  
  openrouter: [
    { 
      id: 'openai/gpt-4o', 
      name: 'GPT-4o (OpenRouter)', 
      description: 'OpenAI via OpenRouter', 
      context_length: LARGE_CONTEXT_LENGTH, 
      is_recommended: true 
    },
    { 
      id: 'anthropic/claude-3.5-sonnet', 
      name: 'Claude 3.5 Sonnet (OpenRouter)', 
      description: 'Anthropic via OpenRouter', 
      context_length: EXTRA_LARGE_CONTEXT_LENGTH, 
      is_recommended: false 
    },
    { 
      id: 'meta-llama/llama-3.1-70b-instruct', 
      name: 'Llama 3.1 70B (OpenRouter)', 
      description: 'Meta via OpenRouter', 
      context_length: 131072, 
      is_recommended: false 
    }
  ],
  
  perplexity: [
    { 
      id: 'llama-3.1-sonar-large-128k-online', 
      name: 'Llama 3.1 Sonar Large 128K Online', 
      description: 'Large online model', 
      context_length: LARGE_CONTEXT_LENGTH, 
      is_recommended: true 
    },
    { 
      id: 'llama-3.1-sonar-small-128k-online', 
      name: 'Llama 3.1 Sonar Small 128K Online', 
      description: 'Small online model', 
      context_length: LARGE_CONTEXT_LENGTH, 
      is_recommended: false 
    },
    { 
      id: 'llama-3.1-sonar-huge-128k-online', 
      name: 'Llama 3.1 Sonar Huge 128K Online', 
      description: 'Huge online model', 
      context_length: LARGE_CONTEXT_LENGTH, 
      is_recommended: false 
    }
  ],
  
  qwen: [
    { 
      id: 'qwen-max', 
      name: 'Qwen Max', 
      description: 'Most capable', 
      context_length: MEDIUM_CONTEXT_LENGTH, 
      is_recommended: true 
    },
    { 
      id: 'qwen-plus', 
      name: 'Qwen Plus', 
      description: 'Balanced performance', 
      context_length: MEDIUM_CONTEXT_LENGTH, 
      is_recommended: false 
    },
    { 
      id: 'qwen-turbo', 
      name: 'Qwen Turbo', 
      description: 'Fast and efficient', 
      context_length: SMALL_CONTEXT_LENGTH, 
      is_recommended: false 
    }
  ],
  
  together: [
    { 
      id: 'meta-llama/Llama-3-70b-chat-hf', 
      name: 'Llama 3 70B Chat', 
      description: 'Large language model', 
      context_length: SMALL_CONTEXT_LENGTH, 
      is_recommended: true 
    },
    { 
      id: 'meta-llama/Llama-3-8b-chat-hf', 
      name: 'Llama 3 8B Chat', 
      description: 'Smaller, faster model', 
      context_length: SMALL_CONTEXT_LENGTH, 
      is_recommended: false 
    },
    { 
      id: 'mistralai/Mixtral-8x7B-Instruct-v0.1', 
      name: 'Mixtral 8x7B Instruct', 
      description: 'Mixture of experts', 
      context_length: MEDIUM_CONTEXT_LENGTH, 
      is_recommended: false 
    }
  ],
  
  vertex: [
    { 
      id: 'gemini-1.5-pro', 
      name: 'Gemini 1.5 Pro', 
      description: 'Google\'s most capable model', 
      context_length: 2000000, 
      is_recommended: true 
    },
    { 
      id: 'gemini-1.5-flash', 
      name: 'Gemini 1.5 Flash', 
      description: 'Fast and efficient', 
      context_length: 1000000, 
      is_recommended: false 
    },
    { 
      id: 'gemini-1.0-pro', 
      name: 'Gemini 1.0 Pro', 
      description: 'Previous generation', 
      context_length: MEDIUM_CONTEXT_LENGTH, 
      is_recommended: false 
    }
  ],
  
  xai: [
    { 
      id: 'grok-beta', 
      name: 'Grok Beta', 
      description: 'xAI\'s flagship model', 
      context_length: LARGE_CONTEXT_LENGTH, 
      is_recommended: true 
    },
    { 
      id: 'grok-vision-beta', 
      name: 'Grok Vision Beta', 
      description: 'Vision-capable model', 
      context_length: LARGE_CONTEXT_LENGTH, 
      is_recommended: false 
    }
  ],
  
  ollama: [
    { 
      id: 'llama3.1', 
      name: 'Llama 3.1', 
      description: 'Open source LLM', 
      context_length: DEFAULT_CONTEXT_LENGTH, 
      is_recommended: true 
    },
    { 
      id: 'mistral', 
      name: 'Mistral', 
      description: 'Efficient transformer', 
      context_length: SMALL_CONTEXT_LENGTH, 
      is_recommended: false 
    },
    { 
      id: 'codellama', 
      name: 'Code Llama', 
      description: 'Code-focused', 
      context_length: 16384, 
      is_recommended: false 
    }
  ],
  
  lmstudio: [
    { 
      id: 'local-model', 
      name: 'Local Model', 
      description: 'Your local model', 
      context_length: DEFAULT_CONTEXT_LENGTH, 
      is_recommended: true 
    }
  ]
};

/**
 * Get fallback models for a specific provider
 */
function getFallbackModels(providerId) {
  return FALLBACK_MODELS[providerId] || [];
}

/**
 * Get model description by provider and model ID
 */
function getModelDescription(providerId, modelId) {
  var models = FALLBACK_MODELS[providerId] || [];
  for (var i = 0; i < models.length; i++) {
    if (models[i].id === modelId) {
      return models[i].description;
    }
  }
  return '';
}

/**
 * Get model context length by provider and model ID
 */
function getModelContextLength(providerId, modelId) {
  var models = FALLBACK_MODELS[providerId] || [];
  for (var i = 0; i < models.length; i++) {
    if (models[i].id === modelId) {
      return models[i].context_length;
    }
  }
  return DEFAULT_CONTEXT_LENGTH;
}

/**
 * Check if a model is recommended for a provider
 */
function isModelRecommended(providerId, modelId) {
  var models = FALLBACK_MODELS[providerId] || [];
  for (var i = 0; i < models.length; i++) {
    if (models[i].id === modelId) {
      return models[i].is_recommended;
    }
  }
  return false;
}
