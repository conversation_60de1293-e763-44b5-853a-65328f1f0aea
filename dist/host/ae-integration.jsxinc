/**
 * SahAI CEP Extension V2 - ExtendScript Main File
 * This file handles communication between the CEP panel and Adobe applications
 */

// Import constants and model data
// @ts-nocheck
#include "constants.jsx"
#include "modelData.jsx"

// Global namespace for SahAI ExtendScript functions
var SahAI = SahAI || {};

/**
 * Initialize the ExtendScript environment
 */
SahAI.init = function() {
    try {
        // Set up error handling
        $.level = 1; // Enable debugging

        // Ensure extension directory exists
        var settingsDir = new Folder(EXTENSION_DIR_PATH);
        if (!settingsDir.exists) {
            if (!settingsDir.create()) {
                throw new Error("Failed to create extension directory: " + EXTENSION_DIR_PATH);
            }
        }

        // Log initialization
        $.writeln("SahAI ExtendScript initialized successfully");

        return {
            success: true,
            message: "ExtendScript initialized",
            version: "2.0.0"
        };
    } catch (error) {
        $.writeln("Error initializing SahAI ExtendScript: " + error.toString());
        return {
            success: false,
            message: error.toString()
        };
    }
};

/**
 * Get application information
 */
SahAI.getAppInfo = function() {
    try {
        var result = {
            success: true,
            data: {
                name: app.name,
                version: app.version,
                locale: app.locale,
                build: app.build || "Unknown"
            }
        };
        return JSON.stringify(result);
    } catch (error) {
        var errorResult = {
            success: false,
            message: error.toString()
        };
        return JSON.stringify(errorResult);
    }
};

/**
 * Execute code in the host application
 * @param {string} code - The code to execute
 * @param {string} language - The programming language (for context)
 */
SahAI.executeCode = function(code, language) {
    try {
        var result;
        
        switch (language.toLowerCase()) {
            case 'javascript':
            case 'extendscript':
                // Execute ExtendScript code
                result = eval(code);
                break;
                
            case 'applescript':
                // Execute AppleScript (macOS only)
                if ($.os.indexOf("Mac") !== -1) {
                    result = app.doScript(code, ScriptLanguage.APPLESCRIPT_LANGUAGE);
                } else {
                    throw new Error("AppleScript is only supported on macOS");
                }
                break;
                
            case 'vbscript':
                // Execute VBScript (Windows only)
                if ($.os.indexOf("Win") !== -1) {
                    result = app.doScript(code, ScriptLanguage.VISUAL_BASIC);
                } else {
                    throw new Error("VBScript is only supported on Windows");
                }
                break;
                
            default:
                throw new Error("Unsupported language: " + language);
        }
        
        var successResult = {
            success: true,
            result: result ? result.toString() : "Code executed successfully",
            language: language
        };
        return JSON.stringify(successResult);
    } catch (error) {
        var errorResult = {
            success: false,
            message: error.toString(),
            language: language
        };
        return JSON.stringify(errorResult);
    }
};

/**
 * Get document information
 */
SahAI.getDocumentInfo = function() {
    try {
        if (!app.activeDocument) {
            var noDocResult = {
                success: false,
                message: "No active document"
            };
            return JSON.stringify(noDocResult);
        }

        var doc = app.activeDocument;
        var result = {
            success: true,
            data: {
                name: doc.name,
                path: doc.fullName ? doc.fullName.toString() : "Unsaved",
                saved: doc.saved,
                modified: doc.modified || false
            }
        };
        return JSON.stringify(result);
    } catch (error) {
        var errorResult = {
            success: false,
            message: error.toString()
        };
        return JSON.stringify(errorResult);
    }
};

/**
 * Show alert dialog
 * @param {string} message - The message to display
 * @param {string} title - The dialog title
 */
SahAI.showAlert = function(message, title) {
    try {
        title = title || "SahAI";
        alert(message, title);
        var result = {
            success: true,
            message: "Alert displayed"
        };
        return JSON.stringify(result);
    } catch (error) {
        var errorResult = {
            success: false,
            message: error.toString()
        };
        return JSON.stringify(errorResult);
    }
};

/**
 * Log message to ExtendScript console
 * @param {string} message - The message to log
 * @param {string} level - Log level (info, warn, error)
 */
SahAI.log = function(message, level) {
    try {
        level = level || "info";
        var timestamp = new Date().toISOString();
        var logMessage = "[" + timestamp + "] [" + level.toUpperCase() + "] " + message;

        $.writeln(logMessage);

        var result = {
            success: true,
            message: "Logged: " + message
        };
        return JSON.stringify(result);
    } catch (error) {
        var errorResult = {
            success: false,
            message: error.toString()
        };
        return JSON.stringify(errorResult);
    }
};

/**
 * Get system information
 */
SahAI.getSystemInfo = function() {
    try {
        var result = {
            success: true,
            data: {
                os: $.os,
                version: $.version,
                buildDate: $.buildDate,
                locale: $.locale,
                memoryUsage: $.memCache
            }
        };
        return JSON.stringify(result);
    } catch (error) {
        var errorResult = {
            success: false,
            message: error.toString()
        };
        return JSON.stringify(errorResult);
    }
};

/**
 * Load settings from file
 */
function loadSettings() {
    try {
        // Ensure directory exists
        var settingsDir = new Folder(EXTENSION_DIR_PATH);
        if (!settingsDir.exists) {
            settingsDir.create();
        }

        var settingsFile = new File(SETTINGS_FILE_PATH);
        if (settingsFile.exists) {
            if (!settingsFile.open("r")) {
                throw new Error("Failed to open settings file for reading");
            }
            var content = settingsFile.read();
            settingsFile.close();

            if (content.trim() === '') {
                return JSON.stringify({ success: true, data: {} });
            }

            var parsedContent = JSON.parse(content);
            return JSON.stringify({ success: true, data: parsedContent });
        }
        return JSON.stringify({ success: true, data: {} });
    } catch (error) {
        $.writeln("Error loading settings: " + error.toString());
        return JSON.stringify({ success: false, message: error.toString(), data: {} });
    }
}

/**
 * Save settings to file
 * @param {Object} settings - Settings object to save
 */
function saveSettings(settings) {
    try {
        var settingsDir = new Folder(EXTENSION_DIR_PATH);
        if (!settingsDir.exists) {
            if (!settingsDir.create()) {
                throw new Error("Failed to create extension directory");
            }
        }

        var settingsFile = new File(SETTINGS_FILE_PATH);
        if (!settingsFile.open("w")) {
            throw new Error("Failed to open settings file for writing");
        }
        settingsFile.write(JSON.stringify(settings));
        settingsFile.close();

        return JSON.stringify({ success: true });
    } catch (error) {
        $.writeln("Error saving settings: " + error.toString());
        return JSON.stringify({ success: false, message: error.toString() });
    }
}

/**
 * Load chat history from file
 */
function loadHistory() {
    try {
        var historyFile = new File(HISTORY_FILE_PATH);
        if (historyFile.exists) {
            historyFile.open("r");
            var content = historyFile.read();
            historyFile.close();
            return JSON.stringify({ success: true, data: JSON.parse(content) });
        }
        return JSON.stringify({ success: true, data: [] });
    } catch (error) {
        $.writeln("Error loading history: " + error.toString());
        return JSON.stringify({ success: false, message: error.toString(), data: [] });
    }
}

/**
 * Save chat history to file
 * @param {Array} history - Array of chat sessions to save
 */
function saveHistory(history) {
    try {
        var settingsDir = new Folder(EXTENSION_DIR_PATH);
        if (!settingsDir.exists) {
            settingsDir.create();
        }

        var historyFile = new File(HISTORY_FILE_PATH);
        historyFile.open("w");
        historyFile.write(JSON.stringify(history));
        historyFile.close();

        return JSON.stringify({ success: true });
    } catch (error) {
        $.writeln("Error saving history: " + error.toString());
        return JSON.stringify({ success: false, message: error.toString() });
    }
}

// Model description and context length functions are now in modelData.jsx

/**
 * List models for different providers
 * @param {string} providerId - Provider identifier
 * @param {string} baseURL - Base URL for the provider
 * @param {string} apiKey - API key for the provider
 */
function listModels(providerId, baseURL, apiKey) {
    try {
        var responseBody;
        var models = [];
        var retries = 3;

        function fetchWithRetry(fetchFn) {
            var maxRetries = 3;
            var currentRetry = 0;

            while (currentRetry <= maxRetries) {
                try {
                    return fetchFn();
                } catch (e) {
                    currentRetry++;
                    if (currentRetry > maxRetries) {
                        // Log the final failure
                        $.writeln('Failed after ' + maxRetries + ' retries: ' + e.toString());
                        throw new Error('Connection failed after ' + maxRetries + ' attempts: ' + e.toString());
                    }
                    // Log retry attempt
                    $.writeln('Retry attempt ' + currentRetry + '/' + maxRetries + ' after error: ' + e.toString());
                    $.sleep(1000); // Wait 1 second before retry
                }
            }
        }

        switch (providerId) {
            case 'ollama':
                // baseURL is like 'http://localhost:11434'
                var ollamaHost = baseURL.replace('http://', '').split(':')[0];
                var ollamaPort = parseInt(baseURL.replace('http://', '').split(':')[1] || '11434', 10);
                responseBody = fetchWithRetry(function() {
                    return makeRequest(ollamaHost, '/api/tags', 'GET', null, ollamaPort);
                });
                var ollamaData = JSON.parse(responseBody);
                // Handle "blob style" models with rich metadata
                if (ollamaData && ollamaData.models) {
                    for (var i = 0; i < ollamaData.models.length; i++) {
                        models.push({
                            id: ollamaData.models[i].name,
                            name: ollamaData.models[i].name,
                            description: 'Size: ' + (ollamaData.models[i].size / 1e9).toFixed(2) + ' GB',
                            contextLength: ollamaData.models[i].details ? ollamaData.models[i].details.parameter_size : 0
                        });
                    }
                }
                // If no models, attempt blob pull (default: llama2:latest)
                if (models.length === 0) {
                    fetchWithRetry(function() {
                        return makeRequest(ollamaHost, '/api/pull', 'POST', null, ollamaPort, JSON.stringify({name: 'llama2:latest'}));
                    });
                    // Reload after pull
                    responseBody = fetchWithRetry(function() {
                        return makeRequest(ollamaHost, '/api/tags', 'GET', null, ollamaPort);
                    });
                    ollamaData = JSON.parse(responseBody);
                    if (ollamaData && ollamaData.models) {
                        for (var j = 0; j < ollamaData.models.length; j++) {
                            models.push({
                                id: ollamaData.models[j].name,
                                name: ollamaData.models[j].name,
                                description: 'Size: ' + (ollamaData.models[j].size / 1e9).toFixed(2) + ' GB',
                                contextLength: ollamaData.models[j].details ? ollamaData.models[j].details.parameter_size : 0
                            });
                        }
                    }
                }
                break;

            case 'openai':
            case 'groq': // Groq uses an OpenAI-compatible endpoint
                var host = (providerId === 'groq') ? 'api.groq.com' : 'api.openai.com';
                var path = (providerId === 'groq') ? '/openai/v1/models' : '/v1/models';
                responseBody = fetchWithRetry(function() {
                    return makeRequest(host, path, 'GET', apiKey);
                });
                var openAIData = JSON.parse(responseBody);
                if (openAIData && openAIData.data) {
                    for (var j = 0; j < openAIData.data.length; j++) {
                        models.push({
                            id: openAIData.data[j].id,
                            name: openAIData.data[j].id,
                            description: getModelDescription(providerId, openAIData.data[j].id),
                            contextLength: getModelContextLength(providerId, openAIData.data[j].id),
                            isRecommended: isModelRecommended(providerId, openAIData.data[j].id)
                        });
                    }
                }
                break;

            case 'lmstudio':
                // LM Studio uses OpenAI-compatible API at localhost:1234
                var lmHost = baseURL.replace('http://', '').split(':')[0];
                var lmPort = parseInt(baseURL.split(':')[1] || '1234', 10);
                responseBody = fetchWithRetry(function() {
                    return makeRequest(lmHost, '/v1/models', 'GET', null, lmPort);
                });
                var lmData = JSON.parse(responseBody);
                if (lmData && lmData.data) {
                    for (var l = 0; l < lmData.data.length; l++) {
                        models.push({
                            id: lmData.data[l].id,
                            name: lmData.data[l].id,
                            description: 'Local LM Studio model',
                            contextLength: DEFAULT_CONTEXT_LENGTH,
                            isRecommended: l === 0 // First model is recommended
                        });
                    }
                } else {
                    // Fallback models for LM Studio
                    models = getFallbackModels('lmstudio');
                }
                break;

            case 'anthropic':
                // Anthropic doesn't have a public models endpoint.
                // Return fallback models from shared data.
                models = getFallbackModels('anthropic');
                break;

            // Add cases for other providers like 'gemini', 'mistral', etc.
            case 'gemini':
                responseBody = fetchWithRetry(function() {
                    return makeRequest('generativelanguage.googleapis.com', '/v1beta/models?key=' + apiKey, 'GET');
                });
                var geminiData = JSON.parse(responseBody);
                if (geminiData && geminiData.models) {
                    for (var k = 0; k < geminiData.models.length; k++) {
                        models.push({
                            id: geminiData.models[k].name.replace('models/', ''),
                            name: geminiData.models[k].displayName || geminiData.models[k].name,
                            description: geminiData.models[k].description || 'Google Gemini model',
                            contextLength: geminiData.models[k].inputTokenLimit || 1000000,
                            isRecommended: geminiData.models[k].name.indexOf('gemini-1.5-pro') !== -1
                        });
                    }
                } else {
                    // Fallback models for Gemini
                    models = getFallbackModels('gemini');
                }
                break;

            default:
                throw new Error("Unsupported provider: " + providerId);
        }

        return JSON.stringify(models);

    } catch (e) {
        // Enhanced error handling with more specific error messages
        var errorMessage = e.toString();
        var errorType = 'UNKNOWN_ERROR';

        // Categorize common errors
        if (errorMessage.indexOf('Failed to connect') !== -1 || errorMessage.indexOf('connection') !== -1) {
            errorType = 'CONNECTION_ERROR';
            errorMessage = 'Cannot connect to ' + providerId + '. Please check if the service is running and accessible.';
        } else if (errorMessage.indexOf('timeout') !== -1 || errorMessage.indexOf('timed out') !== -1) {
            errorType = 'TIMEOUT_ERROR';
            errorMessage = 'Request timed out. The service may be slow or unavailable.';
        } else if (errorMessage.indexOf('JSON') !== -1 || errorMessage.indexOf('parse') !== -1) {
            errorType = 'PARSE_ERROR';
            errorMessage = 'Invalid response from ' + providerId + '. The service may be misconfigured.';
        } else if (errorMessage.indexOf('Unsupported provider') !== -1) {
            errorType = 'UNSUPPORTED_PROVIDER';
            errorMessage = 'Provider "' + providerId + '" is not supported.';
        }

        $.writeln('Error in loadModelsForProvider(' + providerId + '): ' + errorMessage);

        return JSON.stringify({
            error: true,
            message: errorMessage,
            type: errorType,
            providerId: providerId,
            originalError: e.toString()
        });
    }
}

// getFallbackModels function is now in modelData.jsx

/**
 * Makes an HTTP request using ExtendScript's Socket object.
 * @param {string} host The domain name (e.g., 'api.openai.com').
 * @param {string} path The API path (e.g., '/v1/models').
 * @param {string} method The HTTP method ('GET', 'POST', etc.).
 * @param {string} [apiKey] The Bearer token API key.
 * @param {number} [port=443] The port number (443 for HTTPS).
 * @param {string} [body] The request body for POST requests.
 * @returns {string} The raw JSON response string.
 */
function makeRequest(host, path, method, apiKey, port, body) {
    port = port || HTTPS_DEFAULT_PORT;
    var conn = new Socket();
    var responseBody = '';

    if (conn.open(host + ':' + port, 'UTF-8', undefined, true)) {
        var headers = [
            method + ' ' + path + ' HTTP/1.1',
            'Host: ' + host,
            'Content-Type: application/json',
            'User-Agent: SahAI-CEP-Extension/2.0',
            'Accept: application/json',
            'Cache-Control: no-cache'
        ];

        if (apiKey) {
            headers.push('Authorization: Bearer ' + apiKey);
        }

        // Add content length for POST requests
        if (body && method === 'POST') {
            headers.push('Content-Length: ' + body.length);
        }

        // Send headers
        conn.write(headers.join('\r\n') + '\r\n\r\n');

        // Send body if present
        if (body && method === 'POST') {
            conn.write(body);
        }

        var response = conn.read();
        conn.close();

        // Extract the JSON body from the HTTP response
        var bodyStartIndex = response.indexOf('\r\n\r\n');
        if (bodyStartIndex > -1) {
            responseBody = response.substring(bodyStartIndex + 4);
        } else {
            // Fallback for responses without headers (e.g., some Ollama setups)
            bodyStartIndex = response.indexOf('{');
            var bodyEndIndex = response.lastIndexOf('}');
            if(bodyStartIndex > -1 && bodyEndIndex > -1) {
               responseBody = response.substring(bodyStartIndex, bodyEndIndex + 1);
            }
        }
        return responseBody;
    }
    throw new Error('Failed to connect to ' + host);
}

/**
 * Helper function to make HTTP requests with timeout and better error handling
 * @param {string} url - URL to fetch
 * @param {Object} headers - Optional headers
 * @param {number} timeout - Timeout in milliseconds (default: 10000)
 * @param {number} retries - Number of retry attempts (default: 2)
 */
function getURL(url, headers, timeout, retries) {
    timeout = timeout || 10000; // Default 10 second timeout (reduced for better UX)
    retries = retries || 2; // Default 2 retries

    var lastError = null;

    for (var attempt = 0; attempt <= retries; attempt++) {
        try {
            $.writeln("Making HTTP request to: " + url + " (attempt " + (attempt + 1) + "/" + (retries + 1) + ")");

            var http = new XMLHttpRequest();
            var startTime = new Date().getTime();

            // Enhanced timeout handling for ExtendScript
            var timeoutId = null;
            var timedOut = false;

            // Manual timeout implementation since XMLHttpRequest.timeout may not work
            if (timeout > 0) {
                timeoutId = setTimeout(function() {
                    timedOut = true;
                    try {
                        http.abort();
                    } catch (e) {
                        // Ignore abort errors
                    }
                }, timeout);
            }

            http.open("GET", url, false); // Synchronous request

            // Set default headers with better compatibility
            try {
                http.setRequestHeader("User-Agent", "SahAI-CEP-Extension/2.0");
                http.setRequestHeader("Accept", "application/json");
                http.setRequestHeader("Cache-Control", "no-cache");

                // Add CORS headers for local providers
                if (url.indexOf('localhost') !== -1 || url.indexOf('127.0.0.1') !== -1) {
                    http.setRequestHeader("Access-Control-Allow-Origin", "*");
                }
            } catch (headerError) {
                $.writeln("Warning: Could not set some headers: " + headerError.toString());
            }

            // Set custom headers
            if (headers) {
                for (var key in headers) {
                    if (headers.hasOwnProperty(key)) {
                        try {
                            http.setRequestHeader(key, headers[key]);
                        } catch (headerError) {
                            $.writeln("Warning: Could not set header " + key + ": " + headerError.toString());
                        }
                    }
                }
            }

            // Send request
            http.send();

            // Clear timeout
            if (timeoutId) {
                clearTimeout(timeoutId);
            }

            // Check if request timed out
            if (timedOut) {
                throw new Error("Request timed out after " + timeout + "ms");
            }

            var endTime = new Date().getTime();
            var duration = endTime - startTime;
            $.writeln("HTTP response status: " + http.status + " (took " + duration + "ms)");

            if (http.status >= 200 && http.status < 300) {
                return { success: true, data: http.responseText, duration: duration };
            } else if (http.status === 401) {
                return { success: false, message: "Unauthorized - Invalid API key" };
            } else if (http.status === 403) {
                return { success: false, message: "Forbidden - Access denied" };
            } else if (http.status === 404) {
                return { success: false, message: "Not Found - API endpoint not found" };
            } else if (http.status === 429) {
                return { success: false, message: "Rate Limited - Too many requests" };
            } else if (http.status >= 500) {
                return { success: false, message: "Server Error - Please try again later" };
            } else {
                throw new Error("HTTP " + http.status + " - " + http.statusText);
            }
        } catch (error) {
            lastError = error;
            var errorMessage = error.toString();
            $.writeln("HTTP request error (attempt " + (attempt + 1) + "): " + errorMessage);

            // Don't retry on certain errors
            if (errorMessage.indexOf('401') !== -1 || errorMessage.indexOf('403') !== -1 ||
                errorMessage.indexOf('Unauthorized') !== -1 || errorMessage.indexOf('Forbidden') !== -1) {
                break; // Don't retry auth errors
            }

      // Wait before retry (exponential backoff)
      if (attempt < retries) {
        var delay = Math.min(1000 * Math.pow(2, attempt), 3000); // Max 3s delay
        $.writeln("Retrying in " + delay + "ms...");

        // Use a more efficient delay mechanism
        var startDelay = new Date().getTime();
        var currentDelay = 0;
        while (currentDelay < delay) {
          // Small sleep to prevent blocking the thread
          $.sleep(10);
          currentDelay = new Date().getTime() - startDelay;
        }
      }
        }
    }

    // All attempts failed
    var errorMessage = lastError ? lastError.toString() : "Unknown error";
    if (errorMessage.indexOf('timeout') !== -1 || errorMessage.indexOf('timed out') !== -1) {
        return { success: false, message: "Request timed out after " + timeout + "ms" };
    } else if (errorMessage.indexOf('network') !== -1 || errorMessage.indexOf('connection') !== -1) {
        return { success: false, message: "Network connection error" };
    } else {
        return { success: false, message: errorMessage };
    }
}

/**
 * Fetch models for a specific provider (wrapper for listModels)
 * This function is called by the ProviderBridge in cepIntegration.ts
 * @param {string} providerId - Provider identifier
 * @param {string} baseURL - Base URL for the provider
 * @param {string} apiKey - API key for the provider
 */
function fetchModels(providerId, baseURL, apiKey) {
    try {
        $.writeln("fetchModels called with providerId: " + providerId + ", baseURL: " + (baseURL || 'default') + ", apiKey: " + (apiKey ? 'provided' : 'not provided'));

        // Call the existing listModels function
        var result = listModels(providerId, baseURL, apiKey);
        var parsedResult = JSON.parse(result);

        // Transform the result to match expected format
        if (parsedResult.ok) {
            return JSON.stringify(parsedResult.models);
        } else {
            return JSON.stringify({ error: parsedResult.error || 'Failed to fetch models' });
        }
    } catch (error) {
        $.writeln("Error in fetchModels: " + error.toString());
        return JSON.stringify({ error: error.toString() });
    }
}

// Initialize SahAI when script loads
try {
    $.writeln("=== SahAI Extension Loading ===");
    SahAI.init();
    $.writeln("=== SahAI Extension Loaded Successfully ===");
} catch (error) {
    $.writeln("=== SahAI Extension Load Error: " + error.toString() + " ===");
}
